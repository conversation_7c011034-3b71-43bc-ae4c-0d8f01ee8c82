<!--printer.wxml-->
<view class="container">
  <!-- 自定义导航栏 -->
  <view class="custom-navbar" style="padding-top: {{statusBarHeight}}px;">
    <view class="navbar-content" style="height: {{navBarHeight}}px;">
      <image class="navbar-logo" src="/static/images/logo.png" mode="aspectFit"></image>
      <view class="navbar-title">标签打印</view>
    </view>
  </view>

  <!-- 隐藏的Canvas -->
  <view class="hidden-canvas">
    <canvas width='{{templateWidth*pixelRatio}}' height='{{templateHeight*pixelRatio}}' style="width:{{templateWidth}}px;height:{{templateHeight}}px" canvas-id="Canvas" id="Canvas"></canvas>
    <canvas type="2d" style="width:{{barCodeWidth}}px;height:{{barCodeHeight}}px" class="ls-barcode-canvas" canvas-id="barCodels" id="barCodels"></canvas>
    <canvas style="width:{{qrCodeWidth}}px; height: {{qrCodeHeight}}px;" canvas-id="qrCode"></canvas>
  </view>

  <!-- 页面内容区域 -->
  <view class="page-content" style="padding-bottom: 60px;">
      <!-- 打印机状态栏 -->
    <view class="printer-status {{printerStatus}} {{isScanning ? 'scanning' : ''}} {{isConnecting ? 'connecting' : ''}} {{printerStatus === 'disconnecting' ? 'disconnecting' : ''}}">
      <view class="status-accent"></view>
      <view wx:if="{{isScanning}}" class="status-text">
        <text class="status-icon">🔍</text>
        <text>正在搜索打印机...</text>
      </view>
      <view wx:elif="{{isConnecting || printerStatus === 'connecting'}}" class="status-text">
        <text class="status-icon">🔗</text>
        <text>正在连接打印机...</text>
      </view>
      <view wx:elif="{{printerStatus === 'disconnecting'}}" class="status-text">
        <text class="status-icon">🔌</text>
        <text>正在断开连接...</text>
      </view>
      <view wx:elif="{{printerStatus === 'disconnected'}}" class="status-text">
        <text class="status-icon">⚠️</text>
        <text>未连接打印机</text>
        <view class="status-actions">
          <view class="connect-now-btn {{(isScanning || isConnecting || printerStatus === 'disconnecting') ? 'disabled' : ''}}" bindtap="startConnectPrinter">
            <text>去连接</text>
          </view>
        </view>
      </view>
      <view wx:elif="{{printerStatus === 'printing'}}" class="status-text">
        <text class="status-icon">🖨️</text>
        <text>正在打印中...</text>
        <view class="stop-print-btn" bindtap="stopPrint">停止</view>
      </view>
      <view wx:elif="{{printerStatus === 'error'}}" class="status-text">
        <text class="status-icon">❌</text>
        <view class="error-info">
          <text class="error-message">{{printerErrorMessage}}</text>
          <text wx:if="{{printerErrorCode}}" class="error-code">(错误码: {{printerErrorCode}})</text>
        </view>
        <view class="status-actions">
          <view class="connect-now-btn {{(isScanning || isConnecting || printerStatus === 'disconnecting') ? 'disabled' : ''}}" bindtap="startConnectPrinter">
            <text>去连接</text>
          </view>
        </view>
      </view>
      <view wx:elif="{{printerStatus === 'connected' && printerErrorMessage}}" class="status-text">
        <text class="status-icon">⚠️</text>
        <view class="error-info">
          <text class="warning-message">已连接【{{printerDeviceSn}}】- {{printerErrorMessage}}</text>
          <text wx:if="{{printerErrorCode}}" class="error-code">(错误码: {{printerErrorCode}})</text>
        </view>
        <view class="status-actions">
          <!-- <view class="refresh-material-btn" bindtap="refreshMaterialInfo">
            <text>刷新</text>
            <text>耗材</text>
          </view> -->
          <view class="disconnect-device-btn" bindtap="disconnectDevice">
            <text>断开连接</text>
          </view>
        </view>
      </view>
      <view wx:else class="status-text">
        <text class="status-icon">✅</text>
        <text>已连接打印机【{{printerDeviceSn}}】</text>
        <view class="status-actions">
          <!-- <view class="refresh-material-btn" bindtap="refreshMaterialInfo">
            <text>刷新</text>
            <text>耗材</text>
          </view> -->
          <view class="disconnect-device-btn" bindtap="disconnectDevice">
            <text>断开连接</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 首次进入未连接的连接提示弹窗 -->
    <view class="modal" wx:if="{{showConnectPrompt && printerStatus !== 'connected' && !showDeviceSelector}}">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">连接设备</text>
          <view class="modal-close" bindtap="skipConnectPrompt">×</view>
        </view>
        <view class="modal-body">
          <view>1. 请确保打印机已打开并显示黄灯。</view>
          <view>2. 请确保手机的蓝牙功能已开启。</view>
          <view>3. 请保持手机与打印机在2米范围内。</view>
          <view>4. 点击【去连接设备】，即可连接打印机。</view>
        </view>
        <view class="modal-actions">
          <view class="btn weak" bindtap="skipConnectPrompt">暂不连接</view>
          <view class="btn primary {{(isScanning || isConnecting || printerStatus === 'disconnecting') ? 'disabled' : ''}}" bindtap="confirmConnectPrompt">去连接设备</view>
        </view>
      </view>
    </view>


    <!-- 标签模版列表 -->
    <view class="template-section">
      <view class="section-title">
        <!-- 分类标签页占位到标题处（仅当有分类数据时显示） -->
        <block wx:if="{{categoryList.length > 0}}">
          <scroll-view class="tabs-scroll" scroll-x="true" enable-flex show-scrollbar="false">
            <view class="tabs-container">
              <view wx:for="{{categoryList}}" wx:key="categoryId"
                    class="tag-item {{selectedCategoryName === item.categoryName ? 'active' : ''}}"
                    data-name="{{item.categoryName}}"
                    bindtap="selectCategory">
                <text class="tag-text">{{item.categoryName}}</text>
              </view>
            </view>
          </scroll-view>
        </block>
        <block wx:else>
          标签模版
        </block>
        <text class="help-icon" bindtap="showTemplateHelpTip">?</text>
      </view>

      <!-- 模版帮助提示气泡 -->
      <view class="template-help-tooltip {{showTemplateHelp ? 'visible' : 'hidden'}}">
        <view class="tooltip-text">左右滑动查看更多模版，点击模版完成选择。如有标签定制需求，请联系客服。</view>
      </view>
      <view class="template-list-container">
        <!-- 滚动提示文字 -->
        <view class="template-scroll-hint {{templateScrollState.showHint ? 'visible' : 'hidden'}}" wx:if="{{templates.length > 1}}">
          <text class="hint-text">{{templateScrollState.hintText}}</text>
        </view>

        <view class="template-list-wrapper">
          <!-- 左侧滚动指示器 -->
          <view class="template-scroll-indicator left {{templateScrollState.canScrollLeft ? 'hidden' : 'visible'}}" wx:if="{{templates.length > 1}}">
            <view class="indicator-bar"></view>
          </view>

          <scroll-view class="template-list"
                       scroll-x="true"
                       scroll-into-view="{{scrollIntoViewId}}"
                       scroll-with-animation
                       scroll-anchoring
                       bindscroll="onTemplateScroll"
                       bindscrolltolower="onTemplateScrollToLower"
                       bindscrolltoupper="onTemplateScrollToUpper">
            <view wx:for="{{templates}}" wx:key="TemplateId"
                  class="template-item {{selectedTemplateId === item.TemplateId ? 'selected' : ''}}"
                  data-index="{{index}}"
                  id="template-{{index}}"
                  bindtap="selectTemplate">
              <view class="template-preview">
                <image src="{{item.Thumbnail || item.PreviewPath}}" mode="aspectFit" class="template-image"></image>
              </view>
              <view class="template-name">{{item.TemplateName}}</view>
            </view>
            <!-- 默认定制项 -->
            <view class="template-item custom-template-item" bindtap="showCustomAdModal">
              <view class="template-preview custom-preview">
                <view class="custom-preview-text">定制标签</view>
              </view>
              <view class="template-name">更多模板...</view>
            </view>
          </scroll-view>

          <!-- 右侧滚动指示器 -->
          <view class="template-scroll-indicator right {{templateScrollState.canScrollRight ? 'hidden' : 'visible'}}" wx:if="{{templates.length > 1}}">
            <view class="indicator-bar"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 预览图 -->
    <view class="preview-section">
      <view class="section-title">
        <text>标签预览</text>
        <!-- 当前选中标签信息 -->
        <view class="current-template-info" wx:if="{{globalSelectedTemplateId}}">
          <text class="template-category clickable" bindtap="jumpToCurrentTemplate">{{globalSelectedCategoryName || '默认分类'}}</text>
          <text class="template-separator"> / </text>
          <text class="template-name clickable" bindtap="jumpToCurrentTemplate">{{globalSelectedTemplateName}}</text>
        </view>
      </view>
      <view class="preview-container">
        <image wx:if="{{previewImagePath}}" src="{{previewImagePath}}" mode="aspectFit" class="preview-image" bindlongpress="savePreviewImage"></image>
        <view wx:else class="preview-placeholder">预览图生成中...</view>
      </view>
    </view>

    <!-- 标签内容输入（动态生成） -->
    <view class="content-section">
      <view class="section-title">标签内容</view>
      <view class="content-form">
        <block wx:for="{{dynamicInputs}}" wx:key="drawIndex">
          <!-- TEXT 输入 -->
          <view class="form-item" wx:if="{{item.format === 'TEXT'}}">
            <text class="form-label">{{item.title}}：</text>
            <view class="input-wrapper">
              <input class="form-input"
                     type="text"
                     value="{{item.value}}"
                     data-index="{{index}}"
                     bindinput="onDynamicTextInput"
                     placeholder="(留空则只打印标签模板)"
                      />
              <text class="char-count">{{item.value.length}}{{item.max ? '/' + item.max : ''}}</text>
            </view>
          </view>

          <!-- DATE 输入 -->
          <view class="form-item" wx:elif="{{item.format === 'DATE'}}">
            <text class="form-label">{{item.title}}：</text>
            <view class="date-control">
              <picker class="form-picker"
                      mode="date"
                      value="{{item.date}}"
                      data-index="{{index}}"
                      bindchange="onDynamicDateChange">
                <view class="picker-text">{{item.date || '(留空则只打印标签模板)'}}
                </view>
              </picker>
              <view class="date-btn" bindtap="onDynamicSetToday" data-index="{{index}}" wx:if="{{!item.date}}">今日</view>
              <view class="clear-btn" bindtap="onDynamicClearDate" data-index="{{index}}" wx:if="{{item.date}}">清空</view>
            </view>
          </view>

          <!-- DATE_TIME 输入 -->
          <view class="form-item" wx:elif="{{item.format === 'DATE_TIME'}}">
            <text class="form-label">{{item.title}}：</text>
            <view class="datetime-container">
              <!-- 日期行 -->
              <view class="datetime-row">
                <view class="date-control">
                  <picker class="form-picker"
                          mode="date"
                          value="{{item.date}}"
                          data-index="{{index}}"
                          bindchange="onDynamicDateChange">
                    <view class="picker-text">{{item.date || '选择日期'}}</view>
                  </picker>
                  <view class="date-btn" bindtap="onDynamicSetTodayDateTime" data-index="{{index}}" wx:if="{{!item.date}}">今天</view>
                  <view class="clear-btn" bindtap="onDynamicClearDate" data-index="{{index}}" wx:if="{{item.date}}">清空</view>
                </view>
              </view>
              <!-- 时间行 -->
              <view class="datetime-row">
                <view class="time-control">
                  <picker class="form-picker time-picker"
                          mode="multiSelector"
                          value="{{[item.timeHour || 0, item.timeMinute || 0, item.timeSecond || 0]}}"
                          range="{{[hourRange, minuteRange, secondRange]}}"
                          data-index="{{index}}"
                          bindchange="onDynamicTimeMultiChange">
                    <view class="picker-text">
                      {{item.time ? item.time + ':' + (item.seconds || '00') : '选择时间'}}
                    </view>
                  </picker>
                  <view class="date-btn" bindtap="onDynamicSetNowDateTime" data-index="{{index}}" wx:if="{{!item.time}}">现在</view>
                  <view class="clear-btn" bindtap="onDynamicClearTime" data-index="{{index}}" wx:if="{{item.time}}">清空</view>
                </view>
              </view>
            </view>
          </view>

          <!-- CHECK 复选 -->
          <view class="form-item" wx:elif="{{item.format === 'CHECK'}}">
            <text class="form-label">{{item.title}}：</text>
            <view class="checkbox-group-inline">
              <block wx:for="{{item.options}}" wx:for-item="opt" wx:for-index="optIndex" wx:key="optIndex">
                <view class="checkbox-item" bindtap="onDynamicCheckToggle" data-idx="{{item.drawIndex}}" data-option="{{opt.label}}">
                  <text class="checkbox-icon">{{ opt.checked ? '☑' : '☐' }}</text>
                  <text class="checkbox-label">{{opt.label}}</text>
                </view>
              </block>
            </view>
          </view>

          <!-- OPTION 单选下拉 -->
          <view class="form-item" wx:elif="{{item.format === 'OPTION'}}">
            <text class="form-label">{{item.title}}：</text>
            <view class="option-container">
              <picker mode="selector" range="{{item.options}}" range-key="label" value="{{item.selectedIndex}}" bindchange="onDynamicOptionSelect" data-idx="{{item.drawIndex}}" class="option-picker">
                <view class="picker-display">
                  {{item.isCustomSelected ? item.customValue : (item.selectedValue || '请选择')}}
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
              <!-- 自定义输入框，仅在选择"其他"时显示 -->
              <view class="custom-input-wrapper" wx:if="{{item.isCustomSelected}}">
                <input class="custom-input"
                       type="text"
                       value="{{item.customValue}}"
                       data-idx="{{item.drawIndex}}"
                       bindinput="onDynamicOptionCustomInput"
                       placeholder="请输入自定义内容" />
                <text class="char-count">{{item.customValue.length}}{{item.max ? '/' + item.max : ''}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 打印份数（非模板内容） -->
        <view class="form-item">
          <text class="form-label">打印份数：</text>
          <view class="copies-control">
            <view class="copies-btn" bindtap="decreaseCopies">-</view>
            <input class="copies-input"
                  type="number"
                  value="{{labelContent.copies}}"
                  bindinput="onCopiesInput"
                  placeholder="1" />
            <view class="copies-btn" bindtap="increaseCopies">+</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部菜单栏 -->
    <view class="bottom-menu">
      <view class="menu-item" bindtap="showContact">
        <view class="menu-icon">📞</view>
        <text class="menu-text">联系我们</text>
      </view>

      <view class="menu-item main-action {{isPrinting ? 'printing' : (printerStatus !== 'connected' || printerStatus === 'disconnecting' ? 'disabled' : 'ready')}}" bindtap="onPrintAction">
        <view class="menu-icon">
          {{isPrinting ? '⏹️' : (printerStatus === 'connected' ? '🖨️' : '🔗')}}
        </view>
        <text class="menu-text-center">
          {{isPrinting ? '停止打印' : (printerStatus === 'connected' ? '开始打印' : '开始打印')}}
        </text>
      </view>
    </view>

    <!-- 设备选择对话框 -->
    <view class="modal" wx:if="{{showDeviceSelector}}">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">选择打印机</text>
          <view wx:if="{{isScanning}}" class="scanning-status">🔍 搜索中...</view>
          <view class="modal-close" bindtap="closeDeviceSelector">×</view>
        </view>
        <view class="device-list">
          <view wx:if="{{blueList.length === 0 && !isScanning}}" class="no-device-tip">
            <text>未找到设备，请确保设备已开启并重新搜索</text>
          </view>
          <view wx:if="{{blueList.length === 0 && isScanning}}" class="scanning-tip">
            <text>正在搜索设备...</text>
          </view>
          <view wx:for="{{blueList}}" wx:key="index"
                class="device-item {{(item.name || item.deviceId) === printerDeviceSn ? 'current-device' : ''}} {{(item.name || item.deviceId) === connectingDeviceId ? 'connecting-device' : ''}} {{isConnecting ? 'disabled' : ''}}"
                data-index="{{index}}"
                bindtap="{{isConnecting ? '' : 'selectDevice'}}">
            <view class="device-info">
              <text class="device-name">{{item.name || item.deviceId}}</text>
              <text wx:if="{{(item.name || item.deviceId) === printerDeviceSn}}" class="device-status">已连接</text>
              <text wx:elif="{{(item.name || item.deviceId) === connectingDeviceId}}" class="device-status connecting">连接中...</text>
            </view>

          </view>
        </view>
      </view>
    </view>

    <!-- 联系我们弹窗 -->
    <view class="modal" wx:if="{{showContactModal}}">
      <view class="modal-content contact-modal">
        <view class="modal-header">
          <text class="modal-title">联系我们</text>
          <view class="modal-close" bindtap="closeContact">×</view>
        </view>
        <view class="contact-info">
          <view class="contact-item" bindtap="copyContact" data-text="400-622-9388">
            <text class="contact-label">服务热线：</text>
            <text class="contact-value">400-622-9388</text>
          </view>
          <view class="contact-item" bindtap="copyContact" data-text="020-89577250">
            <text class="contact-label">服务传真：</text>
            <text class="contact-value">020-89577250</text>
          </view>
          <view class="contact-item" bindtap="copyContact" data-text="<EMAIL>">
            <text class="contact-label">服务邮箱：</text>
            <text class="contact-value"><EMAIL></text>
          </view>
          <view class="contact-item">
            <text class="contact-label">地址：</text>
            <text class="contact-value">广州市海珠区泉塘路 2 号之三（浩诚商务中心）605 惠而信</text>
          </view>
          <view class="contact-item">
            <text class="contact-label">官方小程序：</text>
            <text class="contact-value" bindtap="openOfficialMiniProgram">惠而信</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 简化广告弹窗 -->
    <view class="modal" wx:if="{{showCustomAdModal}}">
      <view class="modal-content ad-modal">
        <!-- 图片和关闭按钮放在同一父容器中 -->
        <view class="ad-image-wrapper">
          <image 
            src="{{adImageUrl}}" 
            mode="widthFix" 
            class="ad-image"
            show-menu-by-longpress
          ></image>
          <!-- 关闭按钮，覆盖在图片右上角 -->
          <view class="image-close" bindtap="closeCustomAdModal">×</view>
        </view>
        <text class="ad-tip">长按图片识别二维码添加客服</text>
      </view>
    </view>
    
  </view> <!-- 页面内容区域结束 -->
</view>
